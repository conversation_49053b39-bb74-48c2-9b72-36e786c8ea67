# MEDHASAKTHI Frontend Dockerfile
# Multi-stage build for optimized production image

# Stage 1: Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies with legacy peer deps to resolve conflicts
ENV HUSKY=0
ENV CI=true
ENV NODE_OPTIONS=--max-old-space-size=4096

# Clear npm cache and ensure clean install
RUN npm cache clean --force
RUN rm -rf node_modules package-lock.json

# Clean install with force to resolve dependency conflicts
RUN npm install --legacy-peer-deps --no-audit --no-fund

# Install missing dependencies explicitly
RUN npm install ajv@^8.12.0 ajv-keywords@^5.1.0 --legacy-peer-deps

# Verify react-scripts is installed
RUN which react-scripts || ls -la node_modules/.bin/react-scripts || echo "react-scripts not found in PATH"

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Stage 2: Production stage
FROM nginx:alpine AS production

# Install curl for health checks
RUN apk add --no-cache curl

# Copy built application from builder stage
COPY --from=builder /app/build /usr/share/nginx/html

# Copy custom nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Set permissions for nginx
RUN chown -R nginx:nginx /usr/share/nginx/html

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
