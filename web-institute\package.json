{"name": "medhasakthi-institute", "version": "1.0.0", "description": "MEDHASAKTHI Institute Admin Portal", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@mui/material": "^5.14.0", "@mui/icons-material": "^5.14.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@mui/x-data-grid": "^6.18.0", "@mui/x-charts": "^6.18.0", "@mui/x-date-pickers": "^6.18.0", "@reduxjs/toolkit": "^1.9.0", "react-redux": "^8.1.0", "redux-persist": "^6.0.0", "react-hook-form": "^7.45.0", "@hookform/resolvers": "^3.3.0", "yup": "^1.3.0", "axios": "^1.5.0", "react-query": "^3.39.0", "react-router-dom": "^6.15.0", "react-hot-toast": "^2.4.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "@types/lodash": "^4.14.0", "recharts": "^2.8.0", "react-csv": "^2.2.2", "file-saver": "^2.0.5", "@types/file-saver": "^2.0.5", "react-dropzone": "^14.2.0", "react-beautiful-dnd": "^13.1.1", "@types/react-beautiful-dnd": "^13.1.4"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}