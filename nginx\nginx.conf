# MEDHASAKTHI Nginx Configuration
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    access_log /var/log/nginx/access.log main;

    # Performance
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 50M;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

    # SSL Configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';" always;

    # Upstream servers
    upstream backend {
        server backend:8000;
        keepalive 32;
    }

    upstream institute_portal {
        server web-institute:3001;
        keepalive 32;
    }

    upstream student_portal {
        server web-student:3000;
        keepalive 32;
    }

    upstream main_frontend {
        server frontend:3000;
        keepalive 32;
    }

    # HTTP to HTTPS redirect
    server {
        listen 80;
        server_name medhasakthi.com www.medhasakthi.com api.medhasakthi.com admin.medhasakthi.com student.medhasakthi.com teacher.medhasakthi.com learn.medhasakthi.com;
        return 301 https://$server_name$request_uri;
    }

    # API Server (Backend)
    server {
        listen 443 ssl http2;
        server_name api.medhasakthi.com;

        ssl_certificate /etc/nginx/ssl/api.medhasakthi.com.crt;
        ssl_certificate_key /etc/nginx/ssl/api.medhasakthi.com.key;

        # API rate limiting
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";
            proxy_http_version 1.1;
            
            # Timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        # Auth endpoints with stricter rate limiting
        location /api/v1/auth/login {
            limit_req zone=login burst=3 nodelay;
            
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Health check
        location /health {
            proxy_pass http://backend;
            access_log off;
        }

        # Documentation
        location /docs {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }

    # Main Domain (Landing Page with Category Selection)
    server {
        listen 443 ssl http2;
        server_name medhasakthi.com www.medhasakthi.com;

        ssl_certificate /etc/nginx/ssl/medhasakthi.com.crt;
        ssl_certificate_key /etc/nginx/ssl/medhasakthi.com.key;

        location / {
            proxy_pass http://main_frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";
            proxy_http_version 1.1;
        }

        # Static assets caching
        location /static/ {
            proxy_pass http://main_frontend;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Health check
        location /health {
            proxy_pass http://main_frontend;
            access_log off;
        }
    }

    # Institute Portal (Admin)
    server {
        listen 443 ssl http2;
        server_name admin.medhasakthi.com;

        ssl_certificate /etc/nginx/ssl/admin.medhasakthi.com.crt;
        ssl_certificate_key /etc/nginx/ssl/admin.medhasakthi.com.key;

        location / {
            proxy_pass http://institute_portal;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";
            proxy_http_version 1.1;
        }

        # Static assets caching
        location /_next/static/ {
            proxy_pass http://institute_portal;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Health check
        location /health {
            proxy_pass http://institute_portal;
            access_log off;
        }
    }

    # Student Portal
    server {
        listen 443 ssl http2;
        server_name student.medhasakthi.com;

        ssl_certificate /etc/nginx/ssl/student.medhasakthi.com.crt;
        ssl_certificate_key /etc/nginx/ssl/student.medhasakthi.com.key;

        location / {
            proxy_pass http://student_portal;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";
            proxy_http_version 1.1;
        }

        # Static assets caching
        location /_next/static/ {
            proxy_pass http://student_portal;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Health check
        location /health {
            proxy_pass http://student_portal;
            access_log off;
        }
    }

    # Teacher Portal
    server {
        listen 443 ssl http2;
        server_name teacher.medhasakthi.com;

        ssl_certificate /etc/nginx/ssl/teacher.medhasakthi.com.crt;
        ssl_certificate_key /etc/nginx/ssl/teacher.medhasakthi.com.key;

        location / {
            proxy_pass http://institute_portal;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";
            proxy_http_version 1.1;
        }

        # Static assets caching
        location /_next/static/ {
            proxy_pass http://institute_portal;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Health check
        location /health {
            proxy_pass http://institute_portal;
            access_log off;
        }
    }

    # Independent Learner Portal
    server {
        listen 443 ssl http2;
        server_name learn.medhasakthi.com;

        ssl_certificate /etc/nginx/ssl/learn.medhasakthi.com.crt;
        ssl_certificate_key /etc/nginx/ssl/learn.medhasakthi.com.key;

        location / {
            proxy_pass http://student_portal;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Connection "";
            proxy_http_version 1.1;
        }

        # Static assets caching
        location /_next/static/ {
            proxy_pass http://student_portal;
            expires 1y;
            add_header Cache-Control "public, immutable";
        }

        # Health check
        location /health {
            proxy_pass http://student_portal;
            access_log off;
        }
    }

    # Default server (catch-all)
    server {
        listen 80 default_server;
        listen 443 ssl default_server;
        server_name _;
        
        ssl_certificate /etc/nginx/ssl/default.crt;
        ssl_certificate_key /etc/nginx/ssl/default.key;
        
        return 444;
    }
}
