# MEDHASAKTHI Local Development Docker Compose
version: '3.8'

services:
  # PostgreSQL Database (same as production)
  postgres:
    image: postgres:15-alpine
    container_name: medhasakthi-postgres-dev
    environment:
      POSTGRES_DB: medhasakthi_dev
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: devpassword123
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - medhasakthi-dev-network
    restart: unless-stopped

  # Redis Cache (same as production)
  redis:
    image: redis:7-alpine
    container_name: medhasakthi-redis-dev
    command: redis-server --appendonly yes --requirepass devredis123
    volumes:
      - redis_dev_data:/data
    ports:
      - "6379:6379"
    networks:
      - medhasakthi-dev-network
    restart: unless-stopped

  # Backend API (development mode with hot reload)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: medhasakthi-backend-dev
    environment:
      - DATABASE_URL=***********************************************/medhasakthi_dev
      - REDIS_URL=redis://:devredis123@redis:6379
      - SECRET_KEY=dev-secret-key-not-for-production
      - JWT_SECRET_KEY=dev-jwt-secret-key
      - ENVIRONMENT=development
      - DEBUG=true
      - CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./backend/uploads:/app/uploads
    depends_on:
      - postgres
      - redis
    networks:
      - medhasakthi-dev-network
    restart: unless-stopped
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload

volumes:
  postgres_dev_data:
  redis_dev_data:

networks:
  medhasakthi-dev-network:
    driver: bridge
