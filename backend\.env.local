# Local Development Environment (No Database Required)
ENVIRONMENT=development
DEBUG=true

# Security Keys (Development Only)
SECRET_KEY=dev-secret-key-not-for-production-32chars
JWT_SECRET_KEY=dev-jwt-secret-key-not-for-production
CSRF_SECRET_KEY=dev-csrf-secret-key-not-for-production

# Database Configuration (SQLite for local development)
DATABASE_URL=sqlite:///./medhasakthi_dev.db

# Redis Configuration (Disabled for local dev)
REDIS_URL=
REDIS_ENABLED=false

# Domain Configuration
DOMAIN=localhost
FRONTEND_URL=http://localhost:3000
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002

# Disable features that require external services
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SENDGRID_API_KEY=

# AI Services (Optional)
OPENAI_API_KEY=

# UPI Payment (Disabled for development)
UPI_ENABLED=false

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Logging
LOG_LEVEL=DEBUG
