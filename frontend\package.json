{"name": "medha<PERSON>kthi-frontend", "version": "1.0.0", "description": "MEDHASAKTHI - World-Class Educational Technology Platform Frontend", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.2.0", "@reduxjs/toolkit": "^2.8.2", "@tanstack/react-query": "^5.83.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "ajv": "^8.12.0", "ajv-keywords": "^5.1.0", "@types/node": "^24.1.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "axios": "^1.11.0", "chart.js": "^4.5.0", "date-fns": "^4.1.0", "formik": "^2.4.6", "framer-motion": "^12.23.9", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lodash": "^4.17.21", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-hot-toast": "^2.5.2", "react-refresh": "^0.14.0", "react-loading-skeleton": "^3.5.0", "react-pdf": "^10.0.1", "react-redux": "^9.2.0", "react-router-dom": "^6.26.0", "react-scripts": "5.0.1", "react-speech-recognition": "^4.0.1", "react-spring": "^10.0.1", "schema-utils": "^4.2.0", "terser-webpack-plugin": "^5.3.9", "react-webcam": "^7.2.0", "recharts": "^3.1.0", "redux-persist": "^6.0.0", "socket.io-client": "^4.8.1", "typescript": "^4.9.5", "uuid": "^11.1.0", "web-vitals": "^5.0.3", "yup": "^1.6.1"}, "devDependencies": {"@types/lodash": "^4.17.20", "@types/uuid": "^10.0.0", "eslint": "^8.57.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "husky": "^9.1.7", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "source-map-explorer": "^2.5.3", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.11"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "npm run build && npx source-map-explorer 'build/static/js/*.js'", "lint": "eslint src --ext .ts,.tsx --max-warnings 50", "lint:fix": "eslint src --ext .ts,.tsx --fix", "lint:check": "eslint src --ext .ts,.tsx --max-warnings 0 --format=table", "format": "prettier --write src/**/*.{ts,tsx,css,md}", "format:check": "prettier --check src/**/*.{ts,tsx,css,md}", "type-check": "tsc --noEmit", "lint-production": "chmod +x lint-production.sh && ./lint-production.sh", "prepare": "echo 'Skipping husky install in development'", "pre-build": "npm run lint && npm run format && npm run type-check", "postinstall": "echo 'Frontend dependencies installed successfully'"}, "eslintConfig": {"extends": ["react-app", "react-app/jest", "prettier"], "rules": {"no-console": "warn", "no-debugger": "warn", "@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "react-hooks/exhaustive-deps": "warn", "prefer-const": "warn", "no-var": "warn", "eqeqeq": "warn", "curly": "warn"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "prettier --write"], "src/**/*.{css,md}": ["prettier --write"]}, "proxy": "http://localhost:8000", "homepage": ".", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["education", "e-learning", "talent-exam", "ai-powered", "react", "typescript", "material-ui"], "author": "MEDHASAKTHI Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/medhasakthi.git"}, "bugs": {"url": "https://github.com/your-org/medhasakthi/issues"}}