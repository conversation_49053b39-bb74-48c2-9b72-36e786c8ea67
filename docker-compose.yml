# MEDHASAKTHI Docker Compose Configuration
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: medhasakthi-postgres
    environment:
      POSTGRES_DB: medhasakthi
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - medhasakthi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d medhasakthi"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: medhasakthi-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - medhasakthi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: medhasakthi-backend
    env_file:
      - .env
    ports:
      - "8000:8000"
    volumes:
      - ./backend/uploads:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - medhasakthi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: medhasakthi-frontend
    environment:
      - REACT_APP_API_URL=https://${DOMAIN:-medhasakthi.com}/api
      - REACT_APP_APP_NAME=MEDHASAKTHI
      - NODE_ENV=production
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - medhasakthi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web Institute Portal
  web-institute:
    build:
      context: ./web-institute
      dockerfile: Dockerfile
    container_name: medhasakthi-web-institute
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://${DOMAIN:-medhasakthi.com}/api
    ports:
      - "3001:3001"
    depends_on:
      - backend
    networks:
      - medhasakthi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Web Student Portal
  web-student:
    build:
      context: ./web-student
      dockerfile: Dockerfile
    container_name: medhasakthi-web-student
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=https://${DOMAIN:-medhasakthi.com}/api
    ports:
      - "3002:3000"
    depends_on:
      - backend
    networks:
      - medhasakthi-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: medhasakthi-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./certificates:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
      - web-institute
      - web-student
    networks:
      - medhasakthi-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  medhasakthi-network:
    driver: bridge
