frontend\src\components\EnhancedLandingPage.tsx:                      <Grid item xs={6} key={index}>
frontend\src\components\EnhancedLandingPage.tsx:            <Grid item xs={12} md={6} key={index}>
frontend\src\components\EnhancedLandingPage.tsx:              <Grid item xs={12} md={4} key={index}>
frontend\src\components\EnhancedLandingPage.tsx:            <Grid item xs={12} md={4} key={index}>
frontend\src\components\EnhancedLandingPage.tsx:              <Grid item xs={12} sm={6} key={category.id}>
frontend\src\components\IndependentLearnerDashboard.tsx:                  <Grid item xs={12} sm={6} key={program.id}>
frontend\src\components\LandingPagePreview.tsx:                    <Grid item xs={6} key={index}>
frontend\src\components\LandingPagePreview.tsx:              <Grid item xs={12} md={6} key={index}>
frontend\src\components\UserFeedbackSystem.tsx:              <Grid item xs={6} sm={3} key={type.value}>
frontend\src\pages\admin\AdminDashboard.tsx:            <Grid item xs={12} sm={6} md={3} key={index}>
frontend\src\pages\admin\AdminDashboard.tsx:                    <Grid item xs={12} sm={6} key={index}>
frontend\src\pages\LandingPage.tsx:              <Grid item xs={6} md={3} key={index}>
frontend\src\pages\LandingPage.tsx:              <Grid item xs={12} md={6} lg={4} key={index}>
frontend\src\pages\LandingPage.tsx:              <Grid item xs={12} md={4} key={index}>
frontend\src\pages\student\StudentDashboard.tsx:            <Grid item xs={12} sm={6} md={3} key={index}>
frontend\src\pages\student\StudentDashboard.tsx:                    <Grid item xs={12} sm={6} md={4} key={index}>
