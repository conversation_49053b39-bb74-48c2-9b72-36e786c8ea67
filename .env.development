# MEDHASAKTHI Development Environment Variables
# DO NOT USE THESE VALUES IN PRODUCTION!

# Environment
ENVIRONMENT=development
DEBUG=true

# Security Keys (Development Only - NOT for production)
SECRET_KEY=dev-secret-key-not-for-production-32chars
JWT_SECRET_KEY=dev-jwt-secret-key-not-for-production
CSRF_SECRET_KEY=dev-csrf-secret-key-not-for-production
BACKUP_ENCRYPTION_KEY=dev-backup-key-not-for-production

# Database Configuration (Development)
POSTGRES_PASSWORD=devpassword123
DATABASE_URL=postgresql://admin:devpassword123@localhost:5432/medhasakthi_dev

# Redis Configuration (Development)
REDIS_PASSWORD=devredis123
REDIS_URL=redis://:devredis123@localhost:6379

# Domain Configuration (Development)
DOMAIN=localhost
FRONTEND_URL=http://localhost:3000
BACKEND_CORS_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:8080

# Email Configuration (Development - Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SENDGRID_API_KEY=optional-for-dev

# AI Services (Optional for development)
OPENAI_API_KEY=sk-your-openai-api-key-here

# UPI Payment (Development)
UPI_ENABLED=false
UPI_PRIMARY_ID=test@paytm

# File Upload
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# Session Configuration
SESSION_TIMEOUT=3600
REFRESH_TOKEN_EXPIRE_DAYS=30

# Rate Limiting (Relaxed for development)
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60

# Logging
LOG_LEVEL=DEBUG
LOG_FORMAT=detailed

# Development Features
ENABLE_SWAGGER=true
ENABLE_REDOC=true
ENABLE_DEBUG_TOOLBAR=true
