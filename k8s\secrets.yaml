apiVersion: v1
kind: Secret
metadata:
  name: medhasakthi-secrets
  namespace: medhasakthi
type: Opaque
data:
  # Base64 encoded values from your .env file
  # These are the actual encoded versions of your secrets

  # Database Configuration
  database-url: ****************************************************************************************

  # Redis Configuration
  redis-url: cmVkaXM6Ly86UmVkaXMyMDI0IUNhY2hlUGFzc0ByZWRpczozNjc5

  # Security Keys
  secret-key: cSNOWTp2PzYmUVtlb3pmO2JQZC9fYXJEMyZuYk9ZQCE=
  jwt-secret-key: fS16Wih5XlJWREcwUHRCdkdKL1lvcHltL25oKGlHTA==
  csrf-secret-key: fFUmeForLzZiSDVZajsqZnZFOngkditvJXJtMXZpfEA=

  # Email Configuration
  smtp-host: c210cC5nbWFpbC5jb20=
  smtp-username: ********************************
  smtp-password: dmZsdSBldnd4IHZxYmEgcXZ0eA==

  # AI Services
  openai-api-key: ****************************************************************************************************************************************************************************************************************************

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: medhasakthi-config
  namespace: medhasakthi
data:
  # Non-sensitive configuration
  DOMAIN: "medhasakthi.com"
  FRONTEND_URL: "https://medhasakthi.com"
  BACKEND_CORS_ORIGINS: "https://medhasakthi.com,https://www.medhasakthi.com"
  FROM_EMAIL: "<EMAIL>"
  FROM_NAME: "MEDHASAKTHI"
  UPI_ENABLED: "true"
  UPI_PRIMARY_ID: "medhasakthi@ybl"
  ALGORITHM: "HS256"
  ACCESS_TOKEN_EXPIRE_MINUTES: "30"
  REFRESH_TOKEN_EXPIRE_DAYS: "7"
  MAX_FILE_SIZE: "10485760"
  UPLOAD_DIR: "uploads"
  RATE_LIMIT_PER_MINUTE: "60"

---
# Instructions for updating secrets:
# 1. Replace the base64 encoded values above with your actual secrets
# 2. To encode a value: echo -n "your-actual-secret" | base64
# 3. Apply to cluster: kubectl apply -f k8s/secrets.yaml
# 4. Verify: kubectl get secrets -n medhasakthi

# Example encoding commands:
# echo -n "****************************************************/medhasakthi" | base64
# echo -n "redis://:Redis2024!CachePass@redis:6379" | base64
# echo -n "your-super-secret-key-32-chars-minimum" | base64
